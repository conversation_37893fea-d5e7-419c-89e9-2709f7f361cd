fixtures:
  - customer_information: # name of the fixture must be provided and be unique
      - customer_id: "125" # Edith <PERSON>

test_cases:
  - test_case: credit card purchase with home delivery
    steps:
    - user: "can you help me buy a credit card now"
      assertions:
        - flow_started: "credit_card_purchase"
        - bot_uttered:
            utter_name: utter_ask_credit_card_type
    - user: "Visa"
      assertions:
        - slot_was_set:
            - name: "credit_card_type"
              value: "Visa"
        - bot_uttered:
            utter_name: utter_ask_delivery_type
    - user: "at home"
      assertions:
        - slot_was_set:
            - name: "delivery_type"
              value: "Home"
        - bot_uttered:
            utter_name: utter_ask_home_address
    - user: "12, street Jeanne, Paris France"
      assertions:
        - slot_was_set:
            - name: "home_address"
              value: "12, street Jeanne, Paris France"
        - bot_uttered:
            utter_name: utter_recap_information
        - bot_uttered:
            utter_name: utter_ask_confirm_recap
    - user: "No"
      assertions:
        - slot_was_set:
            - name: "confirm_recap"
              value: False
        - bot_uttered:
            utter_name: utter_more_help_needed_handoff_pattern

# ====================================================>
  - test_case: credit card purchase with bank delivery
    steps:
    - user: "I want to buy a credit card"
      assertions:
        - flow_started: "credit_card_purchase"
        - bot_uttered:
            utter_name: utter_ask_credit_card_type
    - user: "Mastercard"
      assertions:
        - slot_was_set:
            - name: "credit_card_type"
              value: "Mastercard"
        - bot_uttered:
            utter_name: utter_ask_delivery_type
    - user: "to the closest bank near you"
      assertions:
        - slot_was_set:
            - name: "delivery_type"
              value: "Bank"
        - bot_uttered:
            utter_name: utter_recap_information
        - bot_uttered:
            utter_name: utter_ask_confirm_recap
    - user: "Yes"
      assertions:
        - slot_was_set:
            - name: "confirm_recap"
              value: True
        - bot_uttered:
            utter_name: utter_credit_card_success
