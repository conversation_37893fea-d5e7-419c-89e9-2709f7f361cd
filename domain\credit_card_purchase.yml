version: "3.1"

slots:
  credit_card_type:
    type: text
    mappings:
      - type: from_llm

  delivery_type:
    type: text
    mappings:
      - type: from_llm

  home_address:
    type: text
    mappings:
      - type: from_llm

  confirm_recap:
    type: bool
    mappings:
      - type: from_llm

responses:
  utter_ask_credit_card_type:
    - text: |
        Certainly, I can help you with that.
        what kind of credit card you are looking to buy
      buttons:
        - title: "Visa"
          payload: "/SetSlots(credit_card_type=Visa)"
        - title: "Mastercard"
          payload: "/SetSlots(credit_card_type=Mastercard)"
        - title: "American express"
          payload: "/SetSlots(credit_card_type=American express)"

  utter_ask_delivery_type:
    - text: |
        Okay let's move to the next step of the credit card purchase.
        Would you like your new credit card to be delivered
      buttons:
        - title: "at home"
          payload: "/SetSlots(delivery_type=Home)"
        - title: "to the closest bank near you"
          payload: "/SetSlots(delivery_type=Bank)"

  utter_ask_home_address:
    - text: |
        Understood!
        Can you provide me your full address please?



  utter_ask_confirm_recap:
    - text: |
        Can you confirm that all the information captured above is accurate:
      buttons:
        - title: "Yes"
          payload: "/SetSlots(confirm_recap=True)"
        - title: "No"
          payload: "/SetSlots(confirm_recap=False)"

  utter_credit_card_success:
    - text: |
        Perfect! Your credit card order has been processed successfully.
        You will receive your {credit_card_type} card at your specified location.
        Thank you for choosing our services!

actions:
  - action_process_credit_card_order
  - action_get_customer_info
  - action_recap_information
