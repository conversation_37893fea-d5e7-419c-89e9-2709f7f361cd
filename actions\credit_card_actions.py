from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.events import SlotSet
from rasa_sdk.executor import CollectingDispatcher
import logging

class ActionProcessCreditCardOrder(Action):
    def name(self):
        return "action_process_credit_card_order"

    def run(self, dispatcher, tracker, domain):
        # Get the credit card information from slots
        credit_card_type = tracker.get_slot("credit_card_type")
        delivery_type = tracker.get_slot("delivery_type")
        home_address = tracker.get_slot("home_address")
        customer_id = tracker.get_slot("customer_id")

        logging.info(f"Processing credit card order for customer {customer_id}")
        logging.info(f"Card type: {credit_card_type}, Delivery: {delivery_type}, Address: {home_address}")

        # Here you would typically:
        # 1. Validate the information
        # 2. Process the order in your backend system
        # 3. Generate order confirmation
        # 4. Send confirmation email/SMS

        # For now, we'll just log the order and return success
        return []

class ActionRecapInformation(Action):
    def name(self):
        return "action_recap_information"

    def run(self, dispatcher, tracker, domain):
        # Get the credit card information from slots
        credit_card_type = tracker.get_slot("credit_card_type")
        delivery_type = tracker.get_slot("delivery_type")
        home_address = tracker.get_slot("home_address")

        # Build the recap message dynamically
        recap_message = "Let's make sure all information provided are in order\n"
        recap_message += f"Credit card type : {credit_card_type}\n"
        recap_message += f"Delivery type: {delivery_type}"

        # Add home address only if delivery type is Home and address exists
        if delivery_type == "Home" and home_address:
            recap_message += f"\nHome address : {home_address}"

        # Send the recap message
        dispatcher.utter_message(text=recap_message)

        return []
