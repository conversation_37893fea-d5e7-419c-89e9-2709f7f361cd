flows:
  credit_card_purchase:
    description: help the user purchase a new credit card
    steps:
      - collect: credit_card_type
        ask_before_filling: True
      - collect: delivery_type
        ask_before_filling: True
        next:
          - if: slots.delivery_type == "Home"
            then:
              - collect: home_address
                ask_before_filling: True
              - action: utter_recap_information
              - collect: confirm_recap
                ask_before_filling: True
                next:
                  - if: slots.confirm_recap
                    then:
                      - action: utter_credit_card_success
                        next: "END"
                  - else:
                    - action: utter_more_help_needed_handoff_pattern
                      next: "END"
          - else:
            - action: utter_recap_information
            - collect: confirm_recap
              ask_before_filling: True
              next:
                - if: slots.confirm_recap
                  then:
                    - action: utter_credit_card_success
                      next: "END"
                - else:
                  - action: utter_more_help_needed_handoff_pattern
                    next: "END"
