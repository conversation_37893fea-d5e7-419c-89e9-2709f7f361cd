flows:

  pattern_session_start: # this is a pattern that will be triggered in the begining of the session
    description: Flow for starting the conversation
    name: pattern session start
    nlu_trigger:
      - intent: session_start
    steps:
      - action: action_get_customer_info
      - action: utter_session_start
      - action: action_session_start

  pattern_completed: # this a pattern that is triggered at the end of every flow
    description: all flows have been completed and there is nothing else to be done
    name: pattern completed
    steps:
      - noop: True # this means no operation and when this command is detected CALM won't do any actions. it can also be replaced by an utter.
        next: END

  pattern_human_handoff:
    description: Conversation repair flow for switching users to a human agent if their request can't be handled
    name: pattern human handoff
    steps:
      - action: utter_more_help_needed_handoff_pattern
